"""
多Agent系统基础类定义
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import uuid
import asyncio
from datetime import datetime

class TaskStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class AgentType(Enum):
    COORDINATOR = "coordinator"
    CODE = "code"
    DATA = "data"
    NLP = "nlp"
    VISUALIZATION = "visualization"
    TEST = "test"
    DOCUMENTATION = "documentation"

@dataclass
class Task:
    """任务数据结构"""
    id: str
    title: str
    description: str
    task_type: str
    priority: int = 1
    dependencies: List[str] = None
    required_skills: List[str] = None
    estimated_time: int = 0  # 预估时间（分钟）
    status: TaskStatus = TaskStatus.PENDING
    assigned_agent: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.required_skills is None:
            self.required_skills = []
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()

@dataclass
class AgentCapability:
    """Agent能力定义"""
    skill: str
    proficiency: float  # 0.0-1.0
    description: str
    tools: List[str] = None
    
    def __post_init__(self):
        if self.tools is None:
            self.tools = []

class BaseAgent(ABC):
    """Agent基础类"""
    
    def __init__(self, agent_id: str, agent_type: AgentType, name: str):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.name = name
        self.capabilities: List[AgentCapability] = []
        self.current_tasks: List[str] = []
        self.completed_tasks: List[str] = []
        self.is_busy = False
        self.max_concurrent_tasks = 1
        
    def add_capability(self, capability: AgentCapability):
        """添加能力"""
        self.capabilities.append(capability)
    
    def get_skill_proficiency(self, skill: str) -> float:
        """获取特定技能的熟练度"""
        for cap in self.capabilities:
            if cap.skill == skill:
                return cap.proficiency
        return 0.0
    
    def can_handle_task(self, task: Task) -> float:
        """评估是否能处理任务，返回匹配度分数"""
        if len(self.current_tasks) >= self.max_concurrent_tasks:
            return 0.0
            
        if not task.required_skills:
            return 0.5  # 默认匹配度
            
        total_score = 0.0
        for skill in task.required_skills:
            total_score += self.get_skill_proficiency(skill)
            
        return total_score / len(task.required_skills)
    
    @abstractmethod
    async def execute_task(self, task: Task) -> Dict[str, Any]:
        """执行任务的抽象方法"""
        pass
    
    async def process_task(self, task: Task) -> Dict[str, Any]:
        """处理任务的通用流程"""
        try:
            self.current_tasks.append(task.id)
            self.is_busy = True
            task.status = TaskStatus.IN_PROGRESS
            task.assigned_agent = self.agent_id
            task.updated_at = datetime.now()
            
            print(f"[{self.name}] 开始执行任务: {task.title}")
            
            # 执行具体任务
            result = await self.execute_task(task)
            
            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.result = result
            task.updated_at = datetime.now()
            
            self.current_tasks.remove(task.id)
            self.completed_tasks.append(task.id)
            
            if len(self.current_tasks) == 0:
                self.is_busy = False
                
            print(f"[{self.name}] 完成任务: {task.title}")
            return result
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.result = {"error": str(e)}
            task.updated_at = datetime.now()
            
            if task.id in self.current_tasks:
                self.current_tasks.remove(task.id)
            if len(self.current_tasks) == 0:
                self.is_busy = False
                
            print(f"[{self.name}] 任务执行失败: {task.title}, 错误: {e}")
            raise e

class MessageType(Enum):
    TASK_ASSIGNMENT = "task_assignment"
    TASK_COMPLETION = "task_completion"
    TASK_FAILURE = "task_failure"
    STATUS_UPDATE = "status_update"
    COORDINATION_REQUEST = "coordination_request"

@dataclass
class Message:
    """Agent间通信消息"""
    id: str
    sender_id: str
    receiver_id: str
    message_type: MessageType
    content: Dict[str, Any]
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class MessageBus:
    """消息总线，用于Agent间通信"""
    
    def __init__(self):
        self.subscribers: Dict[str, List[callable]] = {}
        self.message_history: List[Message] = []
    
    def subscribe(self, agent_id: str, callback: callable):
        """订阅消息"""
        if agent_id not in self.subscribers:
            self.subscribers[agent_id] = []
        self.subscribers[agent_id].append(callback)
    
    async def publish(self, message: Message):
        """发布消息"""
        self.message_history.append(message)
        
        # 发送给特定接收者
        if message.receiver_id in self.subscribers:
            for callback in self.subscribers[message.receiver_id]:
                await callback(message)
        
        # 广播消息（如果接收者为空）
        if message.receiver_id == "*":
            for agent_id, callbacks in self.subscribers.items():
                if agent_id != message.sender_id:
                    for callback in callbacks:
                        await callback(message)

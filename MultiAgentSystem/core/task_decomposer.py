"""
智能任务分解器
根据任务特性自动拆分为适合不同专长Agent的子任务
"""
import re
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from .agent_base import Task, TaskStatus, AgentType
import uuid

@dataclass
class TaskPattern:
    """任务模式定义"""
    pattern: str  # 正则表达式模式
    task_type: str
    required_skills: List[str]
    agent_type: AgentType
    priority: int = 1
    estimated_time: int = 30  # 默认30分钟

class TaskDecomposer:
    """任务分解器"""
    
    def __init__(self):
        self.task_patterns = self._initialize_patterns()
        self.decomposition_rules = self._initialize_rules()
    
    def _initialize_patterns(self) -> List[TaskPattern]:
        """初始化任务模式"""
        return [
            # 代码相关任务
            TaskPattern(
                pattern=r"(编写|开发|实现|创建|构建).*(代码|程序|函数|类|模块|API|接口)",
                task_type="code_development",
                required_skills=["programming", "software_design"],
                agent_type=AgentType.CODE,
                estimated_time=60
            ),
            TaskPattern(
                pattern=r"(修复|调试|解决).*(bug|错误|问题|异常)",
                task_type="code_debugging",
                required_skills=["debugging", "problem_solving"],
                agent_type=AgentType.CODE,
                estimated_time=45
            ),
            TaskPattern(
                pattern=r"(重构|优化|改进).*(代码|性能|算法)",
                task_type="code_optimization",
                required_skills=["code_optimization", "performance_tuning"],
                agent_type=AgentType.CODE,
                estimated_time=90
            ),
            
            # 数据相关任务
            TaskPattern(
                pattern=r"(分析|处理|清洗|转换).*(数据|dataset|csv|json|数据库)",
                task_type="data_processing",
                required_skills=["data_analysis", "data_processing"],
                agent_type=AgentType.DATA,
                estimated_time=45
            ),
            TaskPattern(
                pattern=r"(统计|计算|汇总|聚合).*(指标|数据|结果)",
                task_type="data_analysis",
                required_skills=["statistics", "data_analysis"],
                agent_type=AgentType.DATA,
                estimated_time=30
            ),
            
            # NLP相关任务
            TaskPattern(
                pattern=r"(翻译|总结|摘要|提取).*(文本|内容|信息)",
                task_type="text_processing",
                required_skills=["nlp", "text_analysis"],
                agent_type=AgentType.NLP,
                estimated_time=20
            ),
            TaskPattern(
                pattern=r"(生成|创建|编写).*(文档|报告|说明|描述)",
                task_type="content_generation",
                required_skills=["writing", "content_creation"],
                agent_type=AgentType.NLP,
                estimated_time=40
            ),
            
            # 可视化相关任务
            TaskPattern(
                pattern=r"(创建|生成|绘制|制作).*(图表|图形|可视化|dashboard|界面)",
                task_type="visualization",
                required_skills=["data_visualization", "ui_design"],
                agent_type=AgentType.VISUALIZATION,
                estimated_time=35
            ),
            
            # 测试相关任务
            TaskPattern(
                pattern=r"(测试|验证|检查|质量保证).*(功能|性能|安全|代码)",
                task_type="testing",
                required_skills=["testing", "quality_assurance"],
                agent_type=AgentType.TEST,
                estimated_time=40
            ),
            
            # 文档相关任务
            TaskPattern(
                pattern=r"(编写|创建|更新).*(文档|手册|说明书|API文档)",
                task_type="documentation",
                required_skills=["technical_writing", "documentation"],
                agent_type=AgentType.DOCUMENTATION,
                estimated_time=50
            )
        ]
    
    def _initialize_rules(self) -> Dict[str, Any]:
        """初始化分解规则"""
        return {
            "web_application": {
                "subtasks": [
                    {"type": "code_development", "title": "后端API开发", "skills": ["backend", "api_design"]},
                    {"type": "code_development", "title": "前端界面开发", "skills": ["frontend", "ui_development"]},
                    {"type": "testing", "title": "功能测试", "skills": ["testing", "qa"]},
                    {"type": "documentation", "title": "API文档编写", "skills": ["technical_writing"]}
                ]
            },
            "data_analysis_project": {
                "subtasks": [
                    {"type": "data_processing", "title": "数据清洗和预处理", "skills": ["data_cleaning"]},
                    {"type": "data_analysis", "title": "统计分析", "skills": ["statistics", "analysis"]},
                    {"type": "visualization", "title": "数据可视化", "skills": ["data_visualization"]},
                    {"type": "content_generation", "title": "分析报告生成", "skills": ["report_writing"]}
                ]
            },
            "machine_learning_model": {
                "subtasks": [
                    {"type": "data_processing", "title": "特征工程", "skills": ["feature_engineering"]},
                    {"type": "code_development", "title": "模型训练代码", "skills": ["ml_programming"]},
                    {"type": "testing", "title": "模型验证", "skills": ["model_validation"]},
                    {"type": "visualization", "title": "结果可视化", "skills": ["ml_visualization"]}
                ]
            }
        }
    
    def analyze_task_complexity(self, task: Task) -> Dict[str, Any]:
        """分析任务复杂度"""
        complexity_indicators = {
            "keywords": len(re.findall(r'\b\w+\b', task.description)),
            "has_multiple_verbs": len(re.findall(r'(创建|开发|分析|测试|文档|可视化)', task.description)) > 1,
            "mentions_multiple_technologies": len(re.findall(r'(API|数据库|前端|后端|机器学习|深度学习)', task.description)) > 1,
            "estimated_complexity": "high" if len(task.description) > 200 else "medium" if len(task.description) > 100 else "low"
        }
        
        return complexity_indicators
    
    def decompose_task(self, task: Task) -> List[Task]:
        """分解任务为子任务"""
        # 分析任务复杂度
        complexity = self.analyze_task_complexity(task)
        
        # 如果任务简单，直接返回原任务
        if complexity["estimated_complexity"] == "low" and not complexity["has_multiple_verbs"]:
            return [task]
        
        # 尝试模式匹配分解
        pattern_subtasks = self._decompose_by_patterns(task)
        if pattern_subtasks:
            return pattern_subtasks
        
        # 尝试规则分解
        rule_subtasks = self._decompose_by_rules(task)
        if rule_subtasks:
            return rule_subtasks
        
        # 智能分解（基于关键词和语义）
        semantic_subtasks = self._decompose_semantically(task)
        if semantic_subtasks:
            return semantic_subtasks
        
        # 如果无法分解，返回原任务
        return [task]
    
    def _decompose_by_patterns(self, task: Task) -> List[Task]:
        """基于模式分解任务"""
        matched_patterns = []
        
        for pattern in self.task_patterns:
            if re.search(pattern.pattern, task.description, re.IGNORECASE):
                matched_patterns.append(pattern)
        
        if len(matched_patterns) <= 1:
            return []
        
        subtasks = []
        for i, pattern in enumerate(matched_patterns):
            subtask = Task(
                id=str(uuid.uuid4()),
                title=f"{task.title} - {pattern.task_type}",
                description=f"执行{pattern.task_type}相关的工作",
                task_type=pattern.task_type,
                required_skills=pattern.required_skills,
                priority=task.priority,
                estimated_time=pattern.estimated_time,
                dependencies=[subtasks[i-1].id] if i > 0 else []
            )
            subtasks.append(subtask)
        
        return subtasks
    
    def _decompose_by_rules(self, task: Task) -> List[Task]:
        """基于预定义规则分解任务"""
        # 检查任务描述是否匹配预定义的项目类型
        for project_type, rule in self.decomposition_rules.items():
            if project_type.replace("_", " ") in task.description.lower():
                return self._create_subtasks_from_rule(task, rule)
        
        return []
    
    def _create_subtasks_from_rule(self, parent_task: Task, rule: Dict[str, Any]) -> List[Task]:
        """根据规则创建子任务"""
        subtasks = []
        
        for i, subtask_def in enumerate(rule["subtasks"]):
            subtask = Task(
                id=str(uuid.uuid4()),
                title=subtask_def["title"],
                description=f"为项目'{parent_task.title}'执行{subtask_def['title']}",
                task_type=subtask_def["type"],
                required_skills=subtask_def["skills"],
                priority=parent_task.priority,
                estimated_time=60,  # 默认估算时间
                dependencies=[subtasks[i-1].id] if i > 0 else []
            )
            subtasks.append(subtask)
        
        return subtasks
    
    def _decompose_semantically(self, task: Task) -> List[Task]:
        """基于语义智能分解任务"""
        # 提取关键动词和名词
        verbs = re.findall(r'(创建|开发|实现|分析|测试|文档|可视化|设计|构建|优化)', task.description)
        nouns = re.findall(r'(API|数据库|前端|后端|模型|报告|图表|文档|测试|界面)', task.description)
        
        if len(verbs) <= 1 or len(nouns) <= 1:
            return []
        
        subtasks = []
        for i, verb in enumerate(verbs):
            # 为每个动词创建一个子任务
            relevant_nouns = [noun for noun in nouns if noun in task.description]
            
            subtask = Task(
                id=str(uuid.uuid4()),
                title=f"{verb}{relevant_nouns[0] if relevant_nouns else '相关组件'}",
                description=f"执行{verb}相关的工作",
                task_type=self._infer_task_type(verb),
                required_skills=self._infer_skills(verb),
                priority=task.priority,
                estimated_time=45,
                dependencies=[subtasks[i-1].id] if i > 0 else []
            )
            subtasks.append(subtask)
        
        return subtasks
    
    def _infer_task_type(self, verb: str) -> str:
        """根据动词推断任务类型"""
        verb_mapping = {
            "创建": "code_development",
            "开发": "code_development", 
            "实现": "code_development",
            "分析": "data_analysis",
            "测试": "testing",
            "文档": "documentation",
            "可视化": "visualization",
            "设计": "design",
            "构建": "code_development",
            "优化": "code_optimization"
        }
        return verb_mapping.get(verb, "general")
    
    def _infer_skills(self, verb: str) -> List[str]:
        """根据动词推断所需技能"""
        skill_mapping = {
            "创建": ["programming", "design"],
            "开发": ["programming", "software_design"],
            "实现": ["programming", "implementation"],
            "分析": ["data_analysis", "statistics"],
            "测试": ["testing", "quality_assurance"],
            "文档": ["technical_writing", "documentation"],
            "可视化": ["data_visualization", "design"],
            "设计": ["system_design", "architecture"],
            "构建": ["programming", "system_integration"],
            "优化": ["performance_tuning", "optimization"]
        }
        return skill_mapping.get(verb, ["general"])
    
    def estimate_total_time(self, subtasks: List[Task]) -> int:
        """估算总执行时间"""
        # 考虑并行执行的可能性
        sequential_time = sum(task.estimated_time for task in subtasks)
        
        # 简单的并行度估算（假设某些任务可以并行）
        parallel_factor = 0.7 if len(subtasks) > 3 else 0.8
        
        return int(sequential_time * parallel_factor)

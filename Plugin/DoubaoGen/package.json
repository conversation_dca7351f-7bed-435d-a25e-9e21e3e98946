{"name": "Doubao-Seedream3-mcp-server", "version": "0.1.0", "description": "A Model Context Protocol server with Doubao Seedream 3.0 that generates images.", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/B3000Kcn/Doubao-Seedream3-mcp-server.git"}, "bin": {"Doubao-Seedream3-mcp-server": "./build/DoubaoGen.js"}, "files": ["build"], "keywords": ["mcp", "do<PERSON>o", "seedream", "images", "ai"], "author": "B3000Kcn", "scripts": {"build": "node -e \"const fs = require('fs'); const path = require('path'); const buildDir = './build'; const srcFile = './DoubaoGen.js'; const targetFile = path.join(buildDir, 'DoubaoGen.js'); if (!fs.existsSync(buildDir)){ fs.mkdirSync(buildDir, { recursive: true }); } fs.copyFileSync(srcFile, targetFile); try { fs.chmodSync(targetFile, '755'); console.log('DoubaoGen build: DoubaoGen.js copied to build directory and chmodded.'); } catch(e) { console.warn('DoubaoGen build: chmod failed, possibly on Windows. File copied.'); }\"", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector build/DoubaoGen.js", "prepublishOnly": "npm run build"}, "dependencies": {"axios": "^1.7.8", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.17.24", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}
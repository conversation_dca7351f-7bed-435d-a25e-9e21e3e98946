{"name": "suno-mcp-server", "version": "0.1.0", "description": "MCP server for generating music with Suno API", "main": "build/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node build/index.js", "dev": "tsc -w & nodemon build/index.js"}, "keywords": ["mcp", "suno", "ai-music"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "latest", "axios": "^1.6.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.11.0", "typescript": "^5.3.3", "nodemon": "^3.0.0"}}